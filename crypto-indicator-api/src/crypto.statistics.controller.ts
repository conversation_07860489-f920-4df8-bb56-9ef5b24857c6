import {BadRequestException, Controller, Get, Logger, Query} from '@nestjs/common';
import {ApiOkResponse, ApiOperation, ApiQuery, ApiTags} from '@nestjs/swagger';
import {CryptoStatisticsService} from './crypto.statistics.service';
import {CryptoCurrencyStatisticsDto} from './dto/crypto-statistics.dto';
import {CryptoCurrencyData} from './api/financial.indicator.api';

@ApiTags('Crypto Statistics')
@Controller()
export class CryptoStatisticsController {
    private readonly logger = new Logger(CryptoStatisticsController.name);

    constructor(
        private readonly cryptoStatisticsService: CryptoStatisticsService,
    ) {
    }

    @Get('/api/v1/crypto/statistics')
    @ApiOperation({summary: 'Get cryptocurrency statistics'})
    @ApiOkResponse({type: [CryptoCurrencyStatisticsDto]})
    async getCryptoStatistics(): Promise<CryptoCurrencyData[]> {
        this.logger.log('Getting crypto statistics');
        return await this.cryptoStatisticsService.getStructuredStatistics();
    }

    @Get('/api/v1/crypto/indicators')
    @ApiOperation({
        summary: 'Get historical indicator data for a specific cryptocurrency',
        description: 'Retrieve time-series technical indicator data for charting and analysis'
    })
    @ApiOkResponse({
        type: CryptoCurrencyStatisticsDto,
        description: 'Historical indicator data retrieved successfully'
    })
    @ApiQuery({
        name: 'symbol',
        description: 'Cryptocurrency symbol (e.g., BTC, ETH, SOL)',
        example: 'ETH'
    })
    @ApiQuery({
        name: 'conversionCurrency',
        description: 'Conversion currency (USD or BTC)',
        example: 'USD'
    })
    async getCryptoIndicators(
        @Query('symbol') symbol: string,
        @Query('conversionCurrency') conversionCurrency: string
    ): Promise<CryptoCurrencyData> {
        this.logger.log(`Getting crypto indicators for ${symbol}/${conversionCurrency}`);

        if (!symbol || !conversionCurrency) {
            throw new BadRequestException('Symbol and conversionCurrency are required');
        }

        return await this.cryptoStatisticsService.getCryptoIndicators(symbol, conversionCurrency);
    }
}
