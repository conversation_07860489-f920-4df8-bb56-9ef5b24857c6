import { Inject, Injectable } from '@nestjs/common';
import {
  DaemonClient,
  CryptoCurrencyData,
} from './api/financial.indicator.api';
import { DataTransformationService } from './services/data-transformation.service';

@Injectable()
export class CryptoStatisticsService {
  constructor(
    @Inject('daemonClient')
    private readonly daemonClient: DaemonClient,
    private readonly transformationService: DataTransformationService,
  ) {}

  async getStructuredStatistics(): Promise<CryptoCurrencyData[]> {
    const daemonResponse = await this.daemonClient.getCryptoStatistics();
    return this.transformationService.transform(daemonResponse);
  }

  async getCryptoIndicators(symbol: string, conversionCurrency: string): Promise<CryptoCurrencyData> {
    const indicatorValues = await this.daemonClient.getCryptoIndicators(symbol, conversionCurrency);

    // Return in consistent CryptoCurrencyData format
    return {
      symbol,
      conversionCurrency,
      indicatorValues
    };
  }
}
