import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { CryptoStatisticsModule } from './crypto.statistics.module';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '..', '..', '.env') });

async function bootstrap() {
  const app = await NestFactory.create(CryptoStatisticsModule);

  const config = new DocumentBuilder()
    .setTitle('Crypto Indicator API')
    .setDescription('API for cryptocurrency technical indicators and statistics')
    .setVersion('1.0')
    .addTag('Crypto Statistics')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);

  const port = process.env.PORT || 6701;
  await app.listen(port);
}
bootstrap();
