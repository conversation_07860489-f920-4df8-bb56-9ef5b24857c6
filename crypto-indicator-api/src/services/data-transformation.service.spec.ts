import { DataTransformationService } from './data-transformation.service';

describe('DataTransformationService', () => {
  let service: DataTransformationService;

  beforeEach(() => {
    service = new DataTransformationService();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should transform daemon response correctly', () => {
    const mockDaemonResponse = {
      indicatorData: [
        {
          symbol: 'BTC',
          conversionCurrency: 'USD',
          indicatorValues: [
            {
              open: 50000,
              high: 52000,
              low: 49000,
              close: 51000,
              volume: 1000000,
              marketCap: 1000000000,
              timestamp: '2025-06-17T23:59:59.999Z',
              name: '2781',
              hl2: 50500,
              p1: true,
              p2: false,
              p3: false,
              color: 'gold',
              smma_15: 50200,
              smma_19: 50100,
              smma_25: 50000,
              smma_29: 49900
            }
          ]
        }
      ],
      latestQuotes: []
    };

    const result = service.transform(mockDaemonResponse);

    expect(result).toHaveLength(1);
    expect(result[0].symbol).toBe('BTC');
    expect(result[0].conversionCurrency).toBe('USD');
    expect(result[0].indicatorValues).toHaveLength(1);
    expect(result[0].indicatorValues[0].close).toBe(51000);
    expect(result[0].indicatorValues[0].color).toBe('green');
  });

  it('should handle empty daemon response', () => {
    const mockDaemonResponse = {
      indicatorData: [],
      latestQuotes: []
    };

    const result = service.transform(mockDaemonResponse);

    expect(result).toHaveLength(0);
  });
});
