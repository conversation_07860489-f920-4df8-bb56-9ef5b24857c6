import {Injectable, Logger} from '@nestjs/common';
import {AppConfig} from '../app.config';

export interface DaemonClient {
    getCryptoStatistics(): Promise<DaemonResponse>;
    getCryptoIndicators(symbol: string, conversionCurrency: string): Promise<IndicatorValue[]>;
}

export interface DaemonResponse {
    indicatorData: CryptoCurrencyData[];
    latestQuotes: any[];
}

export interface CryptoCurrencyData {
    symbol: string;
    conversionCurrency: string;
    indicatorValues: IndicatorValue[];
}

export interface IndicatorValue {
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
    marketCap: number;
    timestamp: string;
    name: string;
    hl2: number;
    p1: boolean;
    p2: boolean;
    p3: boolean;
    color: string;
    smma_15: number;
    smma_19: number;
    smma_25: number;
    smma_29: number;
}

const HOST = AppConfig.INDICATOR_HOST;
const PORT = AppConfig.INDICATOR_PORT;

@Injectable()
class FinancialIndicatorRestClient implements DaemonClient {
    private readonly logger = new Logger(FinancialIndicatorRestClient.name);

    async getCryptoStatistics(): Promise<DaemonResponse> {
        const url: string = `${HOST}:${PORT}/api/v1/crypto/statistics`;
        this.logger.log('Fetching crypto statistics from: ' + url);
        const response: Response = await fetch(url);
        return await response.json();
    }

    async getCryptoIndicators(symbol: string, conversionCurrency: string): Promise<IndicatorValue[]> {
        const url: string = `${HOST}:${PORT}/api/v1/crypto/indicators?symbol=${symbol}&conversionCurrency=${conversionCurrency}`;
        this.logger.log(`Fetching crypto indicators from: ${url}`);
        const response: Response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to fetch indicators: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    }
}

export default FinancialIndicatorRestClient;
