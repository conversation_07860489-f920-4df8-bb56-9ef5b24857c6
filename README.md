# Crypto Indicator Client

Web application for cryptocurrency technical indicators and market statistics.

## Stack

- **Backend**: NestJS, TypeScript, Swagger/OpenAPI
- **Frontend**: React, TypeScript
- **Infrastructure**: Docker, Docker Compose

## Deployment

### Prerequisites

- Docker and Docker Compose
- External financial indicator service on `docker-network`

### Configuration

**All configuration is managed through the `.env` file - no hardcoded defaults.**

Create `.env` file:

```bash
# API Base URL for generated client (used during build)
REACT_APP_API_BASE_URL=http://localhost:6701

# Backend API configuration
API_BASE_URL=http://localhost:6701
PORT=6701

# External indicator service configuration
INDICATOR_HOST=http://financial-indicator-daemon
INDICATOR_PORT=6601

# Docker cache busting
CACHEBUST=1
```

**Important:** All environment variables are required. The application will fail to start if any are missing.

### Local Deployment

```bash
docker network create docker-network
docker-compose up --build -d
```

**Access:**

- Web UI: http://localhost:6701
- API Docs: http://localhost:6701/api-docs

## Development

```bash
# Backend (start first)
cd crypto-indicator-api
npm run start:dev

# Frontend (requires running backend for client generation)
cd crypto-indicator-web
npm run generate-client
npm start
```

**Note:** The frontend client generation prefers to fetch the OpenAPI specification from the live server, but falls back
to a static spec file (`openapi-spec.json`) when the server is not available (e.g., during Docker builds).

## API

- `GET /api/v1/crypto/statistics` - Cryptocurrency data
- `GET /api-docs` - Swagger documentation
- `GET /api-docs-json` - OpenAPI specification (JSON)
