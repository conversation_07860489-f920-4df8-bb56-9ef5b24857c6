export const SMMA_PERIODS = [15, 19, 25, 29] as const;
export type SmmaPeriod = typeof SMMA_PERIODS[number];

export const SIGNAL_COLORS = {
  GOLD: 'gold',
  BLUE: 'blue', 
  GRAY: 'gray',
} as const;

export type SignalColor = typeof SIGNAL_COLORS[keyof typeof SIGNAL_COLORS];

export const CONVERSION_CURRENCIES = ['USD', 'BTC'] as const;
export type ConversionCurrency = typeof CONVERSION_CURRENCIES[number];

export const REFRESH_INTERVAL = 30000; // 30 seconds
export const API_TIMEOUT = 10000; // 10 seconds
