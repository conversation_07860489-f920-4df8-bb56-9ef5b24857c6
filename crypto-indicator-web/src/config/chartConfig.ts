import { ColorType } from 'lightweight-charts';

export const CHART_CONFIG = {
  dimensions: {
    width: 800,
    height: 400,
  },
  layout: {
    background: { type: ColorType.Solid, color: '#ffffff' },
    textColor: '#333',
  },
  grid: {
    vertLines: { color: '#e1e1e1' },
    horzLines: { color: '#e1e1e1' },
  },
  timeScale: {
    timeVisible: true,
    secondsVisible: false,
  },
  rightPriceScale: {
    borderColor: '#cccccc',
  },
  crosshair: {
    mode: 1,
  },
} as const;

export const SERIES_CONFIG = {
  candlestick: {
    upColor: '#26a69a',
    downColor: '#ef5350',
    borderVisible: false,
    wickUpColor: '#26a69a',
    wickDownColor: '#ef5350',
  },
  smma: {
    15: { color: '#FF6B35', lineWidth: 1 },
    19: { color: '#F7931E', lineWidth: 1 },
    25: { color: '#FFD23F', lineWidth: 2 },
    29: { color: '#2196F3', lineWidth: 2 },
  },
} as const;

export const CHART_LEGEND_CONFIG = [
  { color: '#26a69a', label: 'Price (Candlestick)' },
  { color: '#FF6B35', label: 'SMMA-15' },
  { color: '#F7931E', label: 'SMMA-19' },
  { color: '#FFD23F', label: 'SMMA-25' },
  { color: '#2196F3', label: 'SMMA-29' },
] as const;
