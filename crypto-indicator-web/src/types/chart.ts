import type { CryptoCurrencyStatisticsDto } from '../generated';

export interface ChartData {
  time: string;
  value: number;
}

export interface CandlestickData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
}

export interface ChartModalProps {
  data: CryptoCurrencyStatisticsDto;
  onClose: () => void;
}

export interface ChartContainerProps {
  data: CryptoCurrencyStatisticsDto;
}

export interface ChartLegendProps {
  items?: Array<{
    color: string;
    label: string;
  }>;
}

export interface SeriesDataTransformer {
  transformCandlestickData: (indicatorValues: any[]) => CandlestickData[];
  transformSMMAData: (indicatorValues: any[], period: number) => ChartData[];
}
