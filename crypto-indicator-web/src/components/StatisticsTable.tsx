import React, { useEffect, useState } from "react";
import type { CryptoCurrencyStatisticsDto } from "../generated";
import { ChartModal } from "./chart/ChartModal";
import { SignalBadge } from "./signals/SignalBadge";
import { LoadingState } from "./ui/LoadingState";
import { ErrorState } from "./ui/ErrorState";
import { ErrorBoundary } from "./ErrorBoundary";
import { useCryptoData } from "../hooks/useCryptoData";
import { useChartData } from "../hooks/useChartData";
import { formatters } from "../utils/formatters";

// Utility functions for date formatting (keeping only what's not in formatters)
const formatDate = (isoString?: string) =>
    isoString ? new Date(isoString).toLocaleDateString() : "-";

const StatisticsTable: React.FC = () => {
    const [showChart, setShowChart] = useState(false);

    // Use custom hooks for data management
    const { data: statistics, loading, error, fetchData } = useCryptoData();
    const { chartData, chartLoading, chartError, fetchChartData, clearChartData } = useChartData();

    // Handle signal click to show chart
    const handleSignalClick = async (symbol: string, conversionCurrency: string) => {
        await fetchChartData(symbol, conversionCurrency);
        setShowChart(true);
    };

    // Handle chart close
    const closeChart = () => {
        setShowChart(false);
        clearChartData();
    };

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 30000);
        return () => clearInterval(interval);
    }, [fetchData]);

    if (loading && statistics.length === 0) {
        return (
            <div className="app-container">
                <div className="header">
                    <h1>🚀 Crypto Indicator Dashboard</h1>
                    <p>Real-time cryptocurrency technical analysis</p>
                </div>
                <LoadingState message="Loading cryptocurrency data..." />
            </div>
        );
    }

    if (error) {
        return (
            <div className="app-container">
                <div className="header">
                    <h1>🚀 Crypto Indicator Dashboard</h1>
                    <p>Real-time cryptocurrency technical analysis</p>
                </div>
                <ErrorState message={error} onRetry={fetchData} />
            </div>
        );
    }

    const usdStatistics = statistics.filter((crypto) => crypto.conversionCurrency === "USD");
    const btcStatistics = statistics.filter((crypto) => crypto.conversionCurrency === "BTC");

    return (
        <div className="app-container">
            <div className="header">
                <h1>🚀 Crypto Indicator Dashboard</h1>
                <p>Real-time cryptocurrency technical analysis</p>
            </div>

            <div className="table-container">
                <div className="stats-info">
                    <div>
                        <strong>{statistics.length}</strong> cryptocurrencies
                    </div>
                    <button onClick={fetchData} disabled={loading}>
                        {loading ? "Refreshing..." : "Refresh"}
                    </button>
                </div>

                <div style={{overflowX: "auto"}}>
                    <table className="table">
                        <thead>
                        <tr>
                            <th>Cryptocurrency</th>
                            <th>USD Price</th>
                            <th>Market Cap</th>
                            <th>Last Update</th>
                            <th>USD Signal</th>
                            <th>SMMA-29 (USD)</th>
                            <th>BTC Price</th>
                            <th>BTC Signal</th>
                            <th>SMMA-29 (BTC)</th>
                        </tr>
                        </thead>
                        <tbody>
                        {usdStatistics.map((crypto: CryptoCurrencyStatisticsDto) => {
                            const usdData = crypto.indicatorValues.find((el) => el);
                            const btcData: any = btcStatistics.find((el) => el.symbol === crypto.symbol)?.indicatorValues?.find((el) => el);

                            return (
                                <tr key={crypto.symbol}>
                                    <td>
                                        <div className="symbol-cell">
                                            <div className="crypto-icon">
                                                {crypto?.symbol?.slice(0, 2)}
                                            </div>
                                            <strong>{crypto.symbol}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{formatters.price(usdData?.close, 'USD')}</strong>
                                    </td>
                                    <td>
                                        <strong>{formatters.marketCap(usdData?.marketCap)}</strong>
                                    </td>
                                    <td>{formatDate(usdData?.timestamp)}</td>
                                    <td>
                                        <SignalBadge
                                            color={usdData?.color}
                                            onClick={() => handleSignalClick(crypto.symbol, 'USD')}
                                            clickable={true}
                                        />
                                    </td>
                                    <td>{formatters.price(usdData?.smma_29, 'USD')}</td>
                                    <td>
                                        <strong>{formatters.price(btcData?.close, 'BTC')}</strong>
                                    </td>
                                    <td>
                                        <SignalBadge
                                            color={btcData?.color}
                                            onClick={() => handleSignalClick(crypto.symbol, 'BTC')}
                                            clickable={true}
                                        />
                                    </td>
                                    <td>{formatters.price(btcData?.smma_29, 'BTC')}</td>
                                </tr>
                            );
                        })}
                        </tbody>
                    </table>
                </div>
            </div>

            {showChart && chartData && (
                <ErrorBoundary>
                    <ChartModal data={chartData} onClose={closeChart} />
                </ErrorBoundary>
            )}

            {chartLoading && (
                <div className="chart-modal">
                    <div className="chart-modal-content">
                        <LoadingState message="Loading chart data..." />
                    </div>
                </div>
            )}

            {chartError && (
                <div className="chart-modal">
                    <div className="chart-modal-content">
                        <ErrorState
                            message={chartError}
                            onRetry={closeChart}
                            showRetry={true}
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

export default StatisticsTable;
