import React from 'react';

interface LoadingStateProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
}

/**
 * Reusable loading state component
 * Single responsibility: Display loading indicator
 */
export const LoadingState: React.FC<LoadingStateProps> = ({ 
  message = 'Loading...', 
  size = 'medium' 
}) => {
  const sizeClass = `loading-${size}`;

  return (
    <div className={`loading-container ${sizeClass}`}>
      <div className="loading-spinner" />
      <p className="loading-message">{message}</p>
    </div>
  );
};
