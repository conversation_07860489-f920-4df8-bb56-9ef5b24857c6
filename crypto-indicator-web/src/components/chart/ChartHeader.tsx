import React from 'react';

interface ChartHeaderProps {
  symbol: string;
  conversionCurrency: string;
  onClose: () => void;
}

/**
 * Chart modal header component
 * Single responsibility: Display chart title and close button
 */
export const ChartHeader: React.FC<ChartHeaderProps> = ({ 
  symbol, 
  conversionCurrency, 
  onClose 
}) => {
  return (
    <div className="chart-header">
      <h3>{symbol}/{conversionCurrency} - Technical Analysis</h3>
      <button 
        onClick={onClose} 
        className="close-button"
        aria-label="Close chart"
      >
        ×
      </button>
    </div>
  );
};
