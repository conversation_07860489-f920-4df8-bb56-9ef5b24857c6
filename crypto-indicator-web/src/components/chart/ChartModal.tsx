import React from 'react';
import { ChartContainer } from './ChartContainer';
import { ChartHeader } from './ChartHeader';
import { ChartLegend } from './ChartLegend';
import type { ChartModalProps } from '../../types/chart';

/**
 * Chart modal wrapper component
 * Single responsibility: Modal layout and structure
 */
export const ChartModal: React.FC<ChartModalProps> = ({ data, onClose }) => {
  // Handle backdrop click to close modal
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Handle escape key to close modal
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  return (
    <div className="chart-modal" onClick={handleBackdropClick}>
      <div className="chart-modal-content">
        <ChartHeader 
          symbol={data.symbol} 
          conversionCurrency={data.conversionCurrency} 
          onClose={onClose} 
        />
        <ChartContainer data={data} />
        <ChartLegend />
      </div>
    </div>
  );
};
