import React from 'react';
import { useChart } from '../../hooks/useChart';
import type { ChartContainerProps } from '../../types/chart';

/**
 * Pure chart rendering component
 * Single responsibility: Display the chart
 */
export const ChartContainer: React.FC<ChartContainerProps> = ({ data }) => {
  const { containerRef } = useChart(data);

  return (
    <div 
      ref={containerRef} 
      className="chart-container"
      style={{ minHeight: '400px' }}
    />
  );
};
