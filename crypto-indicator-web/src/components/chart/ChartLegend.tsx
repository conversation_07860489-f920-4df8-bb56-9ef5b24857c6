import React from 'react';
import { CHART_LEGEND_CONFIG } from '../../config/chartConfig';
import type { ChartLegendProps } from '../../types/chart';

/**
 * Chart legend component
 * Single responsibility: Display chart legend
 */
export const ChartLegend: React.FC<ChartLegendProps> = ({ items = CHART_LEGEND_CONFIG }) => {
  return (
    <div className="chart-legend">
      {items.map((item, index) => (
        <span key={index} className="legend-item">
          <span 
            className="legend-color" 
            style={{ backgroundColor: item.color }}
          />
          {item.label}
        </span>
      ))}
    </div>
  );
};
