import React from 'react';
import { ChartModal } from './chart/ChartModal';
import type { CryptoCurrencyStatisticsDto } from '../generated';

interface CryptoChartProps {
  data: CryptoCurrencyStatisticsDto;
  onClose: () => void;
}

/**
 * Legacy wrapper component for backward compatibility
 * @deprecated Use ChartModal directly instead
 */
const CryptoChart: React.FC<CryptoChartProps> = ({ data, onClose }) => {
  return <ChartModal data={data} onClose={onClose} />;
};

export default CryptoChart;
