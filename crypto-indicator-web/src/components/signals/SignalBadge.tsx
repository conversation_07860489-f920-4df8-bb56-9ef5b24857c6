import React from 'react';

interface SignalBadgeProps {
  color?: string;
  onClick?: () => void;
  clickable?: boolean;
  title?: string;
}

/**
 * Reusable signal badge component
 * Single responsibility: Display signal with optional click handler
 */
export const SignalBadge: React.FC<SignalBadgeProps> = ({ 
  color, 
  onClick, 
  clickable = false,
  title
}) => {
  const getSignalClass = (signalColor?: string): string => {
    switch (signalColor?.toLowerCase()) {
      case 'gold':
        return 'signal-badge signal-gold';
      case 'blue':
        return 'signal-badge signal-blue';
      case 'gray':
        return 'signal-badge signal-gray';
      default:
        return 'signal-badge signal-default';
    }
  };

  const getEmoji = (signalColor?: string): string => {
    switch (signalColor?.toLowerCase()) {
      case 'gold':
        return '🟡';
      case 'blue':
        return '🔵';
      case 'gray':
        return '⚪';
      default:
        return '❓';
    }
  };

  const className = `${getSignalClass(color)} ${clickable ? 'clickable-signal' : ''}`.trim();
  const displayTitle = title || (clickable ? 'Click to view chart' : undefined);

  return (
    <span 
      className={className}
      onClick={onClick}
      title={displayTitle}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
      onKeyDown={clickable ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick?.();
        }
      } : undefined}
    >
      {getEmoji(color)} {color || 'N/A'}
    </span>
  );
};
