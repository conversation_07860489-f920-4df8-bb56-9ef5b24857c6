import { create<PERSON><PERSON>, IChartApi, CandlestickSeries, LineSeries } from 'lightweight-charts';
import { CHART_CONFIG, SERIES_CONFIG } from '../config/chartConfig';
import { SMMA_PERIODS } from '../constants/indicators';
import { dataTransformers } from './dataTransformers';
import type { CryptoCurrencyStatisticsDto } from '../generated';

/**
 * Chart creation and configuration helpers
 */
export const chartHelpers = {
  /**
   * Create a new chart instance with default configuration
   */
  createChart: (container: HTMLDivElement): IChartApi => {
    return createChart(container, {
      ...CHART_CONFIG,
      width: container.clientWidth || CHART_CONFIG.dimensions.width,
      height: CHART_CONFIG.dimensions.height,
    });
  },

  /**
   * Setup all chart series with data
   */
  setupChartSeries: (chart: IChartApi, data: CryptoCurrencyStatisticsDto): void => {
    // Add candlestick series
    const candlestickSeries = chart.addSeries(CandlestickSeries, SERIES_CONFIG.candlestick);
    const candlestickData = dataTransformers.transformCandlestickData(data.indicatorValues);
    
    if (candlestickData.length > 0) {
      candlestickSeries.setData(candlestickData);
    }

    // Add SMMA series for each period
    SMMA_PERIODS.forEach(period => {
      const seriesConfig = SERIES_CONFIG.smma[period];
      if (seriesConfig) {
        const lineSeries = chart.addSeries(LineSeries, {
          ...seriesConfig,
          title: `SMMA-${period}`,
        });
        
        const smmaData = dataTransformers.transformSMMAData(data.indicatorValues, period);
        if (smmaData.length > 0) {
          lineSeries.setData(smmaData);
        }
      }
    });

    // Fit content to show all data
    chart.timeScale().fitContent();
  },

  /**
   * Handle chart resize
   */
  handleResize: (chart: IChartApi, container: HTMLDivElement): void => {
    chart.applyOptions({
      width: container.clientWidth,
    });
  },

  /**
   * Clean up chart resources
   */
  cleanup: (chart: IChartApi): void => {
    chart.remove();
  },
};
