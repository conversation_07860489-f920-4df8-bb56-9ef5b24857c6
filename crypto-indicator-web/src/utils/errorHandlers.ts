/**
 * Centralized error handling utilities
 */
export const errorHandlers = {
  /**
   * Handle API errors with user-friendly messages
   */
  handleApiError: (error: unknown): string => {
    if (error instanceof Error) {
      // Network errors
      if (error.message.includes('fetch')) {
        return 'Network error. Please check your connection and try again.';
      }
      
      // Timeout errors
      if (error.message.includes('timeout')) {
        return 'Request timed out. Please try again.';
      }
      
      // Server errors
      if (error.message.includes('500')) {
        return 'Server error. Please try again later.';
      }
      
      // Client errors
      if (error.message.includes('400')) {
        return 'Invalid request. Please check your input.';
      }
      
      return error.message;
    }
    
    return 'An unexpected error occurred. Please try again.';
  },

  /**
   * Handle chart rendering errors
   */
  handleChartError: (error: unknown): string => {
    console.error('Chart error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('canvas')) {
        return 'Chart rendering failed. Your browser may not support this feature.';
      }
      
      if (error.message.includes('data')) {
        return 'Invalid chart data. Please try refreshing the data.';
      }
    }
    
    return 'Chart could not be displayed. Please try again.';
  },

  /**
   * Log errors for debugging
   */
  logError: (error: unknown, context: string): void => {
    const timestamp = new Date().toISOString();
    const errorMessage = error instanceof Error ? error.message : String(error);
    const stack = error instanceof Error ? error.stack : undefined;
    
    console.error(`[${timestamp}] Error in ${context}:`, {
      message: errorMessage,
      stack,
      error,
    });
  },
};
