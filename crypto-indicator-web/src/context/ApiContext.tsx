import React, { createContext, useContext, useMemo, ReactNode } from 'react';
import { CryptoIndicatorApiClient } from '../generated';
import { CryptoDataService } from '../services/CryptoDataService';

interface ApiContextType {
  cryptoDataService: CryptoDataService;
  apiClient: CryptoIndicatorApiClient;
}

const ApiContext = createContext<ApiContextType | null>(null);

interface ApiProviderProps {
  children: ReactNode;
}

/**
 * API Context Provider
 * Implements dependency injection for services
 */
export const ApiProvider: React.FC<ApiProviderProps> = ({ children }) => {
  const contextValue = useMemo(() => {
    const apiClient = new CryptoIndicatorApiClient();
    const cryptoDataService = new CryptoDataService(apiClient);
    
    return {
      cryptoDataService,
      apiClient,
    };
  }, []);

  return (
    <ApiContext.Provider value={contextValue}>
      {children}
    </ApiContext.Provider>
  );
};

/**
 * Hook to access API services
 * Throws error if used outside of ApiProvider
 */
export const useApiService = (): ApiContextType => {
  const context = useContext(ApiContext);
  
  if (!context) {
    throw new Error('useApiService must be used within an ApiProvider');
  }
  
  return context;
};
