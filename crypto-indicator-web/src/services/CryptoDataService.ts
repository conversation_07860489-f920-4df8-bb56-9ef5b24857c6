import type { CryptoIndicatorApiClient, CryptoCurrencyStatisticsDto } from '../generated';

/**
 * Service layer for cryptocurrency data operations
 * Implements business logic and abstracts API calls
 */
export class CryptoDataService {
  constructor(private apiClient: CryptoIndicatorApiClient) {}

  /**
   * Fetch all cryptocurrency statistics
   */
  async getStatistics(): Promise<CryptoCurrencyStatisticsDto[]> {
    try {
      return await this.apiClient.CryptoStatisticsController_getCryptoStatistics();
    } catch (error) {
      console.error('Failed to fetch cryptocurrency statistics:', error);
      throw new Error('Unable to fetch cryptocurrency data. Please try again.');
    }
  }

  /**
   * Fetch historical indicator data for a specific cryptocurrency
   */
  async getIndicators(symbol: string, conversionCurrency: string): Promise<CryptoCurrencyStatisticsDto> {
    if (!symbol || !conversionCurrency) {
      throw new Error('Symbol and conversion currency are required');
    }

    try {
      return await this.apiClient.CryptoStatisticsController_getCryptoIndicators(symbol, conversionCurrency);
    } catch (error) {
      console.error(`Failed to fetch indicators for ${symbol}/${conversionCurrency}:`, error);
      throw new Error(`Unable to fetch chart data for ${symbol}/${conversionCurrency}. Please try again.`);
    }
  }

  /**
   * Validate if a symbol and currency combination is supported
   */
  validateSymbolCurrency(symbol: string, conversionCurrency: string): boolean {
    const validSymbols = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT'];
    const validCurrencies = ['USD', 'BTC'];
    
    return validSymbols.includes(symbol.toUpperCase()) && 
           validCurrencies.includes(conversionCurrency.toUpperCase());
  }
}
