import { useState, useCallback } from 'react';
import type { CryptoCurrencyStatisticsDto } from '../generated';
import { useApiClient } from './useApiClient';

interface UseCryptoDataReturn {
  data: CryptoCurrencyStatisticsDto[];
  loading: boolean;
  error: string | null;
  fetchData: () => Promise<void>;
  refetch: () => Promise<void>;
}

/**
 * Hook for managing cryptocurrency statistics data
 * Centralizes data fetching logic and state management
 */
export const useCryptoData = (): UseCryptoDataReturn => {
  const [data, setData] = useState<CryptoCurrencyStatisticsDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const client = useApiClient();

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await client.CryptoStatisticsController_getCryptoStatistics();
      setData(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch cryptocurrency data';
      setError(errorMessage);
      console.error('Error fetching crypto data:', err);
    } finally {
      setLoading(false);
    }
  }, [client]);

  return {
    data,
    loading,
    error,
    fetchData,
    refetch: fetchData,
  };
};
