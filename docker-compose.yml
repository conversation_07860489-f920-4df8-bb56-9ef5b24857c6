version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        CACHEBUST: ${CACHEBUST:-1}
        REACT_APP_API_BASE_URL: ${REACT_APP_API_BASE_URL}
        API_BASE_URL: ${API_BASE_URL}
    networks:
        - docker-network
    ports:
      - "${PORT:-6701}:${PORT:-6701}"
    environment:
      - NODE_ENV=production
      - PORT=${PORT:-6701}
      - INDICATOR_HOST=${INDICATOR_HOST}
      - INDICATOR_PORT=${INDICATOR_PORT}
networks:
  docker-network:
    external: true
