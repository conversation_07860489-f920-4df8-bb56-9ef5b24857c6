# Use a single Node.js image for both applications
FROM node:18

# Set up the React application
WORKDIR /app/crypto-indicator-web
COPY ./crypto-indicator-web/package*.json ./
RUN npm install
# Add cache busting for React build
ARG CACHEBUST=1
# Declare environment variables as build arguments
ARG REACT_APP_API_BASE_URL
ARG API_BASE_URL
# Set them as environment variables for the build process
ENV REACT_APP_API_BASE_URL=$REACT_APP_API_BASE_URL
ENV API_BASE_URL=$API_BASE_URL
COPY ./crypto-indicator-web/ .
# Generate API client and build React app
RUN npm run build

# Set up the NestJS application
WORKDIR /app/crypto-indicator-api
COPY ./crypto-indicator-api/package*.json ./
RUN npm install
COPY ./crypto-indicator-api/ .
RUN npm run build
RUN npm prune --production

# Expose the port for the NestJS application
EXPOSE ${PORT:-6701}

# Start the NestJS application
CMD ["node", "dist/main"]
